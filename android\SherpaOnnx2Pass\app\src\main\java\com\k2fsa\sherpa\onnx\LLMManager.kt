package com.k2fsa.sherpa.onnx

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL

/**
 * 统一的LLM管理器
 * 管理所有LLM提供商，提供统一的API调用接口
 */
object LLMManager {
    private const val TAG = "LLMManager"
    
    // 所有支持的LLM配置
    private val llmConfigs = mapOf(
        LLMProvider.GEMINI to GeminiConfig,
        LLMProvider.DEEPSEEK to DeepSeekConfig
    )
    
    /**
     * 获取当前选择的LLM配置
     */
    fun getCurrentLLMConfig(context: Context): LLMConfig {
        val currentProvider = LLMApiKeyManager.getCurrentProvider(context)
        return llmConfigs[currentProvider] ?: GeminiConfig
    }
    
    /**
     * 获取指定提供商的LLM配置
     */
    fun getLLMConfig(provider: LLMProvider): LLMConfig {
        return llmConfigs[provider] ?: GeminiConfig
    }
    
    /**
     * 检查当前LLM是否可用
     */
    fun isCurrentLLMAvailable(context: Context): Boolean {
        val config = getCurrentLLMConfig(context)
        return config.isApiKeyConfigured(context)
    }
    
    /**
     * 检查指定LLM是否可用
     */
    fun isLLMAvailable(context: Context, provider: LLMProvider): Boolean {
        val config = getLLMConfig(provider)
        return config.isApiKeyConfigured(context)
    }
    
    /**
     * 使用当前LLM进行ASR内容优化
     */
    suspend fun optimizeAsrContent(context: Context, originalContent: String): LLMResult {
        val config = getCurrentLLMConfig(context)
        
        if (!config.isApiKeyConfigured(context)) {
            return LLMResult(
                success = false,
                content = "",
                error = "当前LLM (${config.getProvider().displayName}) 的API密钥未配置"
            )
        }
        
        val prompt = buildAsrOptimizationPrompt(originalContent)
        return callLLMAPI(context, config, prompt)
    }
    
    /**
     * 使用当前LLM生成会议总结
     */
    suspend fun generateMeetingSummary(context: Context, speechContent: String): LLMResult {
        val config = getCurrentLLMConfig(context)
        
        if (!config.isApiKeyConfigured(context)) {
            return LLMResult(
                success = false,
                content = "",
                error = "当前LLM (${config.getProvider().displayName}) 的API密钥未配置"
            )
        }
        
        val prompt = buildMeetingSummaryPrompt(speechContent)
        return callLLMAPI(context, config, prompt)
    }
    
    /**
     * 使用指定LLM进行API调用
     */
    suspend fun callLLMAPI(context: Context, provider: LLMProvider, prompt: String): LLMResult {
        val config = getLLMConfig(provider)
        return callLLMAPI(context, config, prompt)
    }
    
    /**
     * 核心API调用方法
     */
    private suspend fun callLLMAPI(context: Context, config: LLMConfig, prompt: String): LLMResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "使用 ${config.getProvider().displayName} 进行API调用")
                
                val url = URL(config.getApiUrl(context))
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.doOutput = true
                
                // 设置请求头
                val headers = config.getHeaders(context)
                headers.forEach { (key, value) ->
                    connection.setRequestProperty(key, value)
                }
                
                // 构建请求体
                val requestBody = config.buildRequestBody(prompt)
                
                // 发送请求
                val writer = OutputStreamWriter(connection.outputStream)
                writer.write(requestBody)
                writer.flush()
                writer.close()
                
                // 读取响应
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val reader = BufferedReader(InputStreamReader(connection.inputStream))
                    val response = reader.readText()
                    reader.close()
                    
                    // 解析响应
                    val content = config.parseResponse(response)
                    Log.d(TAG, "${config.getProvider().displayName} API调用成功")
                    
                    LLMResult(success = true, content = content)
                } else {
                    val errorReader = BufferedReader(InputStreamReader(connection.errorStream))
                    val errorResponse = errorReader.readText()
                    errorReader.close()
                    
                    val errorMsg = "${config.getProvider().displayName} API调用失败: $responseCode - $errorResponse"
                    Log.e(TAG, errorMsg)
                    
                    LLMResult(success = false, content = "", error = errorMsg)
                }
            } catch (e: Exception) {
                val errorMsg = "${config.getProvider().displayName} API调用异常: ${e.message}"
                Log.e(TAG, errorMsg, e)
                LLMResult(success = false, content = "", error = errorMsg)
            }
        }
    }
    
    /**
     * 构建ASR优化提示词
     */
    private fun buildAsrOptimizationPrompt(originalContent: String): String {
        // 分析内容格式，提供更精准的处理策略
        val formatAnalysis = analyzeContentFormat(originalContent)

        return """
            请对以下语音识别(ASR)内容进行优化和修正，要求：

            1. **语义准确性**：修正明显的识别错误，如同音字错误、语音识别常见错误
            2. **语言流畅性**：优化语句结构，使表达更加自然流畅
            3. **逻辑合理性**：整理语序，确保逻辑清晰、前后连贯
            4. **保持原意**：完全保持原始含义和重要信息，不添加、不删除关键内容
            5. **标点符号**：添加合适的标点符号，提高可读性
            6. **说话人分段**：将同一说话人的连续发言合并为一个段落

            **输出格式要求**：
            - 只合并**连续的**相同说话人发言，保持对话的时间顺序
            - 格式：[说话人姓名]：完整的连续发言内容。
            - 不同说话人之间用空行分隔
            - 如果同一说话人在对话中多次发言但被其他人打断，则分别保留为独立段落
            - 如果没有说话人标识，则按语义和逻辑分段，每段用空行分隔
            - 移除时间戳等技术信息
            - 避免原始的一句话一行的日志格式
            - 整合碎片化的句子为连贯段落

            **内容分析**：
            $formatAnalysis

            **处理策略**：
            - 将**连续的**同一说话人的多行发言合并为一个自然段落
            - 修正语音识别常见错误（同音字、语法错误等）
            - 保持口语化特点，不要过度书面化
            - 确保段落间逻辑连贯，语义完整
            - 严格按照时间顺序，不要重新排列说话人的发言顺序

            **示例说明**：
            原始内容：
            [张三] 大家好
            [张三] 今天我们讨论项目
            [李四] 好的
            [李四] 我先汇报
            [张三] 等一下
            [张三] 我还有补充

            正确输出：
            [张三]：大家好，今天我们讨论项目。

            [李四]：好的，我先汇报。

            [张三]：等一下，我还有补充。

            原始ASR内容：
            $originalContent

            请按照上述格式要求输出优化后的内容：
        """.trimIndent()
    }
    
    /**
     * 分析内容格式，提供精准的处理策略
     */
    private fun analyzeContentFormat(content: String): String {
        val lines = content.split('\n').filter { it.trim().isNotEmpty() }
        val hasSpeakerInfo = lines.any { it.contains(":") && (it.contains("[") || it.contains("说话人")) }
        val hasTimestamp = lines.any { it.matches(Regex(".*\\d{2}:\\d{2}:\\d{2}.*")) }
        val avgLineLength = if (lines.isNotEmpty()) lines.map { it.length }.average() else 0.0
        val fragmentedSentences = lines.count { it.trim().length < 10 }

        return buildString {
            append("内容特征：")
            if (hasSpeakerInfo) append("包含说话人标识，")
            if (hasTimestamp) append("包含时间戳，")
            append("共${lines.size}行，")
            append("平均行长度${avgLineLength.toInt()}字符")
            if (fragmentedSentences > lines.size / 3) {
                append("，存在较多碎片化句子")
            }
            append("。")
        }
    }

    /**
     * 构建会议总结提示词
     */
    private fun buildMeetingSummaryPrompt(speechContent: String): String {
        return """
            请对以下会议内容进行总结，要求：
            1. 提取关键要点和决策
            2. 整理讨论的主要话题
            3. 列出行动项目和责任人（如果有）
            4. 用中文回复，格式清晰

            会议内容：
            $speechContent
        """.trimIndent()
    }
    
    /**
     * 获取所有可用的LLM提供商
     */
    fun getAllProviders(): List<LLMProvider> {
        return LLMProvider.values().toList()
    }
    
    /**
     * 获取当前提供商信息
     */
    fun getCurrentProviderInfo(context: Context): String {
        val provider = LLMApiKeyManager.getCurrentProvider(context)
        val isAvailable = isLLMAvailable(context, provider)
        val status = if (isAvailable) "可用" else "未配置"
        return "${provider.displayName} ($status)"
    }
}
