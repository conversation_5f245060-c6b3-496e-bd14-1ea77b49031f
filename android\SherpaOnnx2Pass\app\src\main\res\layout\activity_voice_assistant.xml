<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/apple_system_grouped_background"
    android:fitsSystemWindows="true">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Header Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="32dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🎙️ 语音助手"
                        android:textAppearance="@style/TextAppearance.VoiceAssistant.Headline"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="准备就绪"
                        android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                        android:textColor="@color/apple_green" />

                </LinearLayout>

                <ImageButton
                    android:id="@+id/btn_settings"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_settings_apple"
                    android:contentDescription="设置"
                    android:layout_marginStart="16dp" />

            </LinearLayout>

            <!-- Recording Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="32dp">

                <!-- Recording Button Container -->
                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp">

                    <!-- Pulse Animation Background (Hidden by default) -->
                    <ImageView
                        android:id="@+id/iv_pulse_animation"
                        android:layout_width="200dp"
                        android:layout_height="200dp"
                        android:layout_gravity="center"
                        android:src="@drawable/pulse_animation"
                        android:visibility="gone" />

                    <!-- Main Recording Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_record"
                        style="@style/Widget.VoiceAssistant.RecordButton"
                        android:layout_gravity="center"
                        android:background="@drawable/record_button_idle"
                        android:contentDescription="开始录音" />

                </FrameLayout>

                <!-- Recording Status Text -->
                <TextView
                    android:id="@+id/tv_recording_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="点击开始录音"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                    android:textColor="@color/apple_secondary_label"
                    android:layout_marginBottom="16dp" />

                <!-- Audio Waveform Visualization (Hidden by default) -->
                <LinearLayout
                    android:id="@+id/ll_waveform"
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:background="@drawable/waveform_background"
                    android:padding="16dp"
                    android:visibility="gone">

                    <!-- Waveform bars will be added programmatically -->

                </LinearLayout>

            </LinearLayout>

            <!-- Real-time Preview Section -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_preview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/apple_blue_ultra_light"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="实时预测"
                        android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                        android:textColor="@color/apple_blue"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_preview"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                        android:textColor="@color/apple_blue_dark"
                        android:minHeight="40dp"
                        android:gravity="center_vertical"
                        android:hint="实时识别结果将在这里显示..." />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Transcription Results Section -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_results"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/apple_system_background"
                app:cardCornerRadius="20dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <!-- Results Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="20dp"
                        android:paddingBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="转录结果"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Title" />

                        <TextView
                            android:id="@+id/tv_word_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 字"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                            android:background="@drawable/count_background"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="4dp" />

                    </LinearLayout>

                    <!-- Results Content -->
                    <ScrollView
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:paddingHorizontal="20dp"
                        android:paddingBottom="20dp">

                        <TextView
                            android:id="@+id/tv_results"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                            android:lineSpacingExtra="6dp"
                            android:textIsSelectable="true"
                            android:minHeight="120dp"
                            android:gravity="top"
                            android:hint="转录结果将在这里显示..." />

                    </ScrollView>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:id="@+id/ll_actions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:visibility="gone">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_summary"
                    style="@style/Widget.VoiceAssistant.Button.Borderless"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="💡 智能总结"
                    android:layout_marginEnd="8dp"
                    app:icon="@drawable/ic_summary_apple"
                    app:iconGravity="textStart" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_clear"
                    style="@style/Widget.VoiceAssistant.Button.Borderless"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="清空"
                    android:textColor="@color/apple_red"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
