package com.k2fsa.sherpa.onnx

import android.content.Context
import org.json.JSONArray
import org.json.JSONObject

/**
 * Gemini API 配置
 * 实现LLMConfig接口，提供Gemini特定的API调用逻辑
 */
object GeminiConfig : LLMConfig {
    // Gemini API 端点
    private const val API_ENDPOINT = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent"

    // 模型名称
    private const val MODEL_NAME = "gemini-2.0-flash-exp"

    override fun getProvider(): LLMProvider = LLMProvider.GEMINI

    override fun isApiKeyConfigured(context: Context): Boolean {
        return LLMApiKeyManager.hasValidApiKey(context, LLMProvider.GEMINI)
    }

    override fun getApiUrl(context: Context): String {
        val apiKey = getApiKey(context)
        return "$API_ENDPOINT?key=$apiKey"
    }

    override fun getApiKey(context: Context): String {
        return LLMApiKeyManager.getApiKey(context, LLMProvider.GEMINI)
    }

    override fun getModelName(): String = MODEL_NAME

    override fun buildRequestBody(prompt: String): String {
        val requestBody = JSONObject().apply {
            put("contents", JSONArray().apply {
                put(JSONObject().apply {
                    put("parts", JSONArray().apply {
                        put(JSONObject().apply {
                            put("text", prompt)
                        })
                    })
                })
            })
            put("generationConfig", JSONObject().apply {
                put("temperature", 0.3)
                put("topK", 20)
                put("topP", 0.8)
                put("maxOutputTokens", 2048)
            })
        }
        return requestBody.toString()
    }

    override fun parseResponse(response: String): String {
        return try {
            val jsonResponse = JSONObject(response)
            val candidates = jsonResponse.getJSONArray("candidates")
            if (candidates.length() > 0) {
                val content = candidates.getJSONObject(0)
                    .getJSONObject("content")
                    .getJSONArray("parts")
                    .getJSONObject(0)
                    .getString("text")
                content.trim()
            } else {
                throw Exception("API返回空结果")
            }
        } catch (e: Exception) {
            throw Exception("解析Gemini响应失败: ${e.message}")
        }
    }

    override fun getHeaders(context: Context): Map<String, String> {
        return mapOf(
            "Content-Type" to "application/json"
        )
    }

    // ==================== 向后兼容方法 ====================

    /**
     * 向后兼容：检查 API 密钥是否已配置
     */
    @JvmStatic
    fun isApiKeyConfiguredCompat(context: Context): Boolean {
        return LLMApiKeyManager.hasValidApiKey(context)
    }

    /**
     * 向后兼容：获取完整的 API URL
     */
    @JvmStatic
    fun getApiUrlCompat(context: Context): String {
        return LLMApiKeyManager.getApiUrl(context)
    }

    /**
     * 向后兼容：获取 API 密钥
     */
    @JvmStatic
    fun getApiKeyCompat(context: Context): String {
        return LLMApiKeyManager.getGeminiApiKey(context)
    }
}
