package com.k2fsa.sherpa.onnx

import android.Manifest
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Typeface
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.*
import org.json.JSONObject
import org.json.JSONArray
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 单模型ASR引擎测试Activity - 优化UI版本
 * 参考FunASR的单模型实现，使用一个模型完成所有识别任务
 * 复用优化版的UI布局和功能，但保持单模型架构
 */
class SingleModelActivity : AppCompatActivity(), SingleModelASREngine.ASRListener {

    companion object {
        private const val TAG = "SingleModelActivity"
        private const val REQUEST_RECORD_AUDIO_PERMISSION = 200
        private const val SAMPLE_RATE = 16000
        private const val BUFFER_SIZE = 1600 // 100ms at 16kHz
    }

    private val permissions: Array<String> = arrayOf(Manifest.permission.RECORD_AUDIO)

    // UI组件 - 适配新的Apple风格布局
    private lateinit var btnRecord: com.google.android.material.button.MaterialButton
    private lateinit var btnClear: com.google.android.material.button.MaterialButton
    private lateinit var btnSummary: com.google.android.material.button.MaterialButton
    private lateinit var btnSettings: android.widget.ImageButton
    private lateinit var tvStatus: TextView
    private lateinit var tvResults: TextView
    private lateinit var tvWordCount: TextView
    private lateinit var tvRecordingStatus: TextView
    private lateinit var llActions: LinearLayout

    // ASR引擎
    private lateinit var asrEngine: SingleModelASREngine
    private var isInitialized = false

    // 自动优化设置
    private var autoOptimize = true

    // 录音相关
    private var audioRecord: AudioRecord? = null
    private val isRecording = AtomicBoolean(false)
    private var recordingThread: Thread? = null

    // 结果管理
    private val recognitionResults = StringBuilder()
    private var wordCount = 0
    private var recognitionCount = 0
    private var sessionStartTime = 0L
    private val timeHandler = Handler(Looper.getMainLooper())
    private var timeUpdateRunnable: Runnable? = null

    // 实时预览状态管理
    private var isShowingPreview = false
    private var currentPreviewText = ""
    private var baseResultsText = ""
    private var previewTimestamp = ""
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            Log.i(TAG, "开始创建SingleModelActivity")

            // 使用优化版的布局
            // setContentView(R.layout.activity_simple_test)
            setContentView(R.layout.activity_voice_assistant)

            // 请求权限
            ActivityCompat.requestPermissions(this, permissions, REQUEST_RECORD_AUDIO_PERMISSION)

            // 初始化UI
            initViews()

            // 加载设置
            loadSettings()

            // 初始化ASR引擎
            initASREngine()

            Log.i(TAG, "SingleModelActivity创建成功")

        } catch (e: Exception) {
            Log.e(TAG, "Activity创建失败", e)
            showToast("创建失败: ${e.message}")

            // 如果初始化失败，退出应用
            finish()
        }
    }
    
    private fun initViews() {
        try {
            // 初始化UI组件 - 适配新的Apple风格布局
            btnRecord = findViewById(R.id.btn_record)
            btnClear = findViewById(R.id.btn_clear)
            btnSummary = findViewById(R.id.btn_summary)
            btnSettings = findViewById(R.id.btn_settings)
            tvStatus = findViewById(R.id.tv_status)
            tvResults = findViewById(R.id.tv_results)
            tvWordCount = findViewById(R.id.tv_word_count)
            tvRecordingStatus = findViewById(R.id.tv_recording_status)
            llActions = findViewById(R.id.ll_actions)

            // 设置点击事件
            btnRecord.setOnClickListener { toggleRecording() }
            btnClear.setOnClickListener { clearResults() }
            btnSummary.setOnClickListener { generateMeetingSummary() }
            btnSettings.setOnClickListener { openSettings() }

            // 设置初始状态
            tvStatus.text = "正在初始化单模型ASR引擎..."
            tvRecordingStatus.text = "点击开始录音"
            tvResults.hint = "转录结果将在这里显示..."
            tvWordCount.text = "0 字"
            llActions.visibility = View.GONE // 初始隐藏操作按钮
            updateUI()

            Log.i(TAG, "UI初始化成功")

        } catch (e: Exception) {
            Log.e(TAG, "UI初始化失败", e)
            showToast("UI初始化失败: ${e.message}")
        }
    }

    /**
     * 加载设置
     */
    private fun loadSettings() {
        try {
            // 从LLMApiKeyManager加载自动优化设置
            autoOptimize = LLMApiKeyManager.getAutoOptimize(this)
            Log.d(TAG, "自动优化设置已加载: $autoOptimize")
        } catch (e: Exception) {
            Log.e(TAG, "加载设置失败", e)
            // 使用默认值
            autoOptimize = true
        }
    }

    /**
     * 打开设置页面
     */
    private fun openSettings() {
        try {
            val intent = android.content.Intent(this, SettingsActivity::class.java)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开设置页面失败", e)
            showToast("打开设置页面失败: ${e.message}")
        }
    }

    /**
     * 初始化单模型ASR引擎
     */
    private fun initASREngine() {
        tvStatus.text = "正在初始化单模型ASR引擎..."

        Thread {
            try {
                // 初始化ASR引擎，传入Context以支持声纹持久化
                asrEngine = SingleModelASREngine(assets, SAMPLE_RATE, context = this@SingleModelActivity)
                asrEngine.setListener(this@SingleModelActivity)

                val success = asrEngine.initialize()

                runOnUiThread {
                    if (success) {
                        isInitialized = true
                        tvStatus.text = "✅ 单模型ASR引擎初始化成功 - 准备就绪"
                        Log.i(TAG, "单模型ASR引擎初始化成功")

                        // 显示恢复的声纹数量
                        val speakerCount = asrEngine.getSpeakerCount()
                        if (speakerCount > 0) {
                            showToast("已恢复 $speakerCount 个声纹")
                            Log.i(TAG, "已恢复 $speakerCount 个声纹")
                        }
                    } else {
                        tvStatus.text = "❌ 单模型ASR引擎初始化失败"
                        showToast("初始化失败")
                        Log.e(TAG, "单模型ASR引擎初始化失败")
                    }
                    updateUI()
                }

            } catch (e: Exception) {
                Log.e(TAG, "单模型ASR引擎初始化异常", e)
                runOnUiThread {
                    tvStatus.text = "❌ 单模型ASR引擎初始化异常: ${e.message}"
                    showToast("初始化失败: ${e.message}")
                    updateUI()
                }
            }
        }.start()
    }
    
    /**
     * 切换录音状态
     */
    private fun toggleRecording() {
        if (!isInitialized) {
            showToast("单模型ASR引擎未初始化")
            return
        }

        if (!isRecording.get()) {
            startRecording()
        } else {
            stopRecording()
        }
    }
    
    /**
     * 开始录音
     */
    private fun startRecording() {
        if (!initMicrophone()) {
            Log.e(TAG, "麦克风初始化失败")
            showToast("麦克风初始化失败")
            return
        }

        try {
            audioRecord?.startRecording()
            isRecording.set(true)
            sessionStartTime = System.currentTimeMillis()

            // 重置ASR引擎状态
            asrEngine.reset()

            // 更新UI - 适配新布局
            tvStatus.text = "🎤 正在录音中..."
            tvRecordingStatus.text = "正在录音，请说话..."
            llActions.visibility = View.GONE // 隐藏操作按钮
            updateUI()
            startTimeUpdate()

            // 开始录音线程
            recordingThread = Thread {
                recordAudio()
            }
            recordingThread?.start()

            Log.i(TAG, "开始录音 - 单模型模式")
            showToast("开始录音")

        } catch (e: Exception) {
            Log.e(TAG, "开始录音失败", e)
            showToast("录音失败: ${e.message}")
            stopRecording()
        }
    }
    
    /**
     * 停止录音
     */
    private fun stopRecording() {
        isRecording.set(false)

        try {
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null

            recordingThread?.join(1000)
            recordingThread = null

            // 停止时间更新
            stopTimeUpdate()

            // 自动优化ASR结果
            if (autoOptimize && recognitionResults.isNotEmpty()) {
                autoOptimizeAsrContent()
            }

            // 更新UI - 适配新布局
            tvStatus.text = "✅ 录音完成"
            tvRecordingStatus.text = "点击开始录音"
            if (recognitionResults.isNotEmpty()) {
                llActions.visibility = View.VISIBLE // 显示操作按钮
            }
            updateUI()

            Log.i(TAG, "录音已停止")
            showToast("录音已停止")

        } catch (e: Exception) {
            Log.e(TAG, "停止录音失败", e)
        }
    }

    /**
     * 初始化麦克风
     */
    private fun initMicrophone(): Boolean {
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            showToast("需要录音权限")
            return false
        }

        val bufferSizeInBytes = AudioRecord.getMinBufferSize(
            SAMPLE_RATE,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_16BIT
        )

        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            SAMPLE_RATE,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_16BIT,
            maxOf(bufferSizeInBytes, BUFFER_SIZE * 4)
        )

        return audioRecord?.state == AudioRecord.STATE_INITIALIZED
    }
    
    /**
     * 处理音频样本
     */
    private fun recordAudio() {
        Log.i(TAG, "开始处理音频样本")

        val buffer = ShortArray(BUFFER_SIZE)

        while (isRecording.get()) {
            try {
                val ret = audioRecord?.read(buffer, 0, buffer.size) ?: 0

                if (ret > 0) {
                    // 转换为Float数组 (归一化到[-1, 1])
                    val samples = FloatArray(ret) { buffer[it] / 32768.0f }

                    // 使用单模型ASR引擎处理音频
                    // 注意：结果处理通过ASRListener接口回调，避免重复处理
                    asrEngine.processAudio(samples)
                }

            } catch (e: Exception) {
                Log.e(TAG, "音频处理失败", e)
                break
            }
        }

        Log.i(TAG, "音频处理结束")
    }
    
    /**
     * 处理ASR识别结果 - 支持说话人识别
     */
    private fun handleASRResult(result: SingleModelASREngine.ASRResult) {
        Log.d(TAG, "收到ASR结果 - 类型: ${result.type}, 文本: '${result.text}', 时间戳: ${result.timestamp}")

        when (result.type) {
            SingleModelASREngine.ResultType.PREVIEW -> {
                // 显示实时预览
                showPreview(result.text)
                // Log.i(TAG, "处理预览结果: '${result.text}'")
            }
            SingleModelASREngine.ResultType.FINAL, SingleModelASREngine.ResultType.ENDPOINT -> {
                // 添加带说话人信息的最终结果
                addFinalResultWithSpeaker(result)
                // Log.i(TAG, "处理最终结果: '${result.text}'")
            }
        }
    }

    /**
     * 显示实时预览 - 直接更新主结果区域的当前行（累积显示）
     */
    private fun showPreview(text: String) {
        if (text.isBlank()) {
            // Log.d(TAG, "showPreview: 收到空白文本，跳过处理")
            return
        }

        // Log.d(TAG, "showPreview: 收到文本='$text', 当前预览状态=$isShowingPreview, 当前累积文本='$currentPreviewText'")

        // 如果不是正在显示预览，开始新的预览（生成新时间戳）
        if (!isShowingPreview) {
            previewTimestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
            currentPreviewText = text  // 开始新预览，直接设置文本
            isShowingPreview = true
            // Log.d(TAG, "showPreview: 开始新预览 - 时间戳=$previewTimestamp, 初始文本='$currentPreviewText'")
        } else {
            // 正在显示预览，累积文本内容（ASR引擎返回的是增量文本）
            currentPreviewText += text  // 累积增量文本
            // Log.d(TAG, "showPreview: 累积预览文本 - 增量='$text', 累积后='$currentPreviewText'")
        }

        // 使用固定的时间戳构建预览行
        val previewLine = "[$previewTimestamp] $currentPreviewText"

        // 更新主结果区域：基础内容 + 当前累积预览行
        val displayText = if (baseResultsText.isNotEmpty()) {
            "$baseResultsText$previewLine"
        } else {
            previewLine
        }

        // Log.d(TAG, "showPreview: 更新显示文本='$displayText'")
        tvResults.text = displayText
    }

     /**
     * 添加最终结果 - 覆盖预览文本或添加新行，支持说话人信息
     */
    private fun addFinalResult(text: String) {
        if (text.isNotBlank()) {
            val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
            val formattedResult = "[$timestamp] $text\n"

            Log.d(TAG, "addFinalResult: 收到最终结果='$text', 当前预览状态=$isShowingPreview, 当前预览文本='$currentPreviewText'")

            // 如果当前正在显示预览，则覆盖预览行
            if (isShowingPreview) {
                // 直接替换最后的预览内容
                recognitionResults.append(formattedResult)
                isShowingPreview = false
                currentPreviewText = ""
                previewTimestamp = ""
                Log.d(TAG, "addFinalResult: 覆盖预览内容，重置预览状态")
            } else {
                // 正常添加新行
                recognitionResults.append(formattedResult)
                Log.d(TAG, "addFinalResult: 正常添加新行")
            }

            // 更新基础结果文本
            baseResultsText = recognitionResults.toString()
            tvResults.text = baseResultsText

            Log.d(TAG, "addFinalResult: 更新基础结果文本，长度=${baseResultsText.length}")

            // 更新统计
            wordCount += text.length
            recognitionCount++
            updateStatistics()
        }
    }

    /**
     * 添加带说话人信息的最终结果
     */
    private fun addFinalResultWithSpeaker(result: SingleModelASREngine.ASRResult) {
        if (result.text.isNotBlank()) {
            val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())

            // 构建结果文本
            val formattedResult = "[$timestamp]-${result.speakerName}: ${result.text}\n"

            // Log.d(TAG, "addFinalResultWithSpeaker: 收到最终结果='${result.text}', 当前预览状态=$isShowingPreview")

            // 如果当前正在显示预览，则覆盖预览行
            if (isShowingPreview) {
                // 直接替换最后的预览内容
                recognitionResults.append(formattedResult)
                isShowingPreview = false
                currentPreviewText = ""
                previewTimestamp = ""
                // Log.d(TAG, "addFinalResultWithSpeaker: 覆盖预览内容，重置预览状态")
            } else {
                // 正常添加新行
                recognitionResults.append(formattedResult)
                // Log.d(TAG, "addFinalResultWithSpeaker: 正常添加新行")
            }

            // 更新基础结果文本
            baseResultsText = recognitionResults.toString()
            tvResults.text = baseResultsText

            // Log.d(TAG, "addFinalResultWithSpeaker: 更新基础结果文本，长度=${baseResultsText.length}")

            // 更新统计
            wordCount += result.text.length
            recognitionCount++
            updateStatistics()
        }
    }
    
    /**
     * 清空结果 - 适配新的UI布局
     */
    private fun clearResults() {
        Log.d(TAG, "clearResults: 清空所有结果和预览状态")

        recognitionResults.clear()
        baseResultsText = ""
        isShowingPreview = false
        currentPreviewText = ""
        previewTimestamp = ""

        tvResults.text = ""
        tvResults.hint = "转录结果将在这里显示..."
        wordCount = 0
        recognitionCount = 0
        llActions.visibility = View.GONE // 隐藏操作按钮
        updateStatistics()
        showToast("结果已清空")

        Log.d(TAG, "clearResults: 清空完成")
    }

    /**
     * 复制结果到剪贴板
     */
    private fun copyResults() {
        if (recognitionResults.isEmpty()) {
            showToast("没有可复制的内容")
            return
        }

        try {
            // 构建要复制的文本内容
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
            val content = buildString {
                append("语音识别结果\n")
                append("生成时间: $timestamp\n")
                append("总字数: $wordCount\n")
                append("识别次数: $recognitionCount\n")
                append("======================================\n\n")
                append(recognitionResults.toString())
            }

            // 复制到剪贴板
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("ASR识别结果", content)
            clipboard.setPrimaryClip(clip)

            showToast("识别结果已复制到剪贴板")

        } catch (e: Exception) {
            Log.e(TAG, "复制结果失败", e)
            showToast("复制失败: ${e.message}")
        }
    }
    
    /**
     * 更新UI状态 - 适配新的Apple风格布局
     */
    private fun updateUI() {
        btnRecord.isEnabled = isInitialized

        // 更新录音按钮状态和样式
        if (isRecording.get()) {
            btnRecord.text = "停止录音"
            btnRecord.setBackgroundResource(R.drawable.record_button_recording)
        } else {
            btnRecord.text = "开始录音"
            btnRecord.setBackgroundResource(R.drawable.record_button_idle)
        }

        // 更新操作按钮状态
        btnClear.isEnabled = !isRecording.get() && recognitionResults.isNotEmpty()
        btnSummary.isEnabled = !isRecording.get() && recognitionResults.isNotEmpty()
        btnSettings.isEnabled = !isRecording.get()

        // 更新操作按钮区域可见性
        llActions.visibility = if (!isRecording.get() && recognitionResults.isNotEmpty()) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }

    /**
     * 更新统计信息 - 适配新的UI布局
     */
    private fun updateStatistics() {
        // 更新字数统计
        tvWordCount.text = "$wordCount 字"

        // 可以在状态栏显示更详细的统计信息
        val elapsed = if (sessionStartTime > 0) {
            (System.currentTimeMillis() - sessionStartTime) / 1000
        } else 0

        val minutes = elapsed / 60
        val seconds = elapsed % 60

        // 在录音时显示时长，停止后显示总结信息
        if (isRecording.get()) {
            tvStatus.text = "🎤 录音中 ${String.format("%02d:%02d", minutes, seconds)}"
        } else if (recognitionResults.isNotEmpty()) {
            tvStatus.text = "✅ 录音完成 - 共 $recognitionCount 次识别"
        }
    }

    /**
     * 开始时间更新
     */
    private fun startTimeUpdate() {
        timeUpdateRunnable = object : Runnable {
            override fun run() {
                if (isRecording.get()) {
                    updateStatistics()
                    timeHandler.postDelayed(this, 1000)
                }
            }
        }
        timeHandler.post(timeUpdateRunnable!!)
    }

    /**
     * 停止时间更新
     */
    private fun stopTimeUpdate() {
        timeUpdateRunnable?.let { timeHandler.removeCallbacks(it) }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    // ASRListener接口实现
    override fun onResult(result: SingleModelASREngine.ASRResult) {
        runOnUiThread {
            handleASRResult(result)
        }
    }

    override fun onError(error: String) {
        runOnUiThread {
            Log.e(TAG, "ASR错误: $error")
            tvStatus.text = "❌ 识别错误: $error"
            showToast("识别错误: $error")
        }
    }

    override fun onStatusChanged(status: String) {
        runOnUiThread {
            tvStatus.text = status
        }
    }

    override fun onSpeakerIdentified(speakerInfo: SingleModelASREngine.SpeakerInfo) {
        runOnUiThread {
            Log.d(TAG, "说话人识别结果: ${speakerInfo.name} (置信度: ${speakerInfo.confidence})")
            // 说话人识别结果已经在ASRResult中处理，这里只记录日志
        }
    }

    override fun onSpeakerRegistered(speakerName: String, success: Boolean) {
        runOnUiThread {
            val message = if (success) {
                "说话人 '$speakerName' 注册成功"
            } else {
                "说话人 '$speakerName' 注册失败"
            }
            Log.i(TAG, message)
            showToast(message)
        }
    }

    override fun onSpeakerRemoved(speakerName: String, success: Boolean) {
        runOnUiThread {
            val message = if (success) {
                "说话人 '$speakerName' 删除成功"
            } else {
                "说话人 '$speakerName' 删除失败"
            }
            Log.i(TAG, message)
            showToast(message)
        }
    }

    override fun onVadStatusChanged(isSpeech: Boolean) {
        runOnUiThread {
            Log.d(TAG, "VAD状态变化: ${if (isSpeech) "检测到语音" else "静音"}")
            // VAD状态变化，可以用于UI指示，暂时只记录日志
        }
    }

    // override fun onResume() {
    //     super.onResume()
    //     // 重新加载设置，以便从设置页面返回时更新配置
    //     loadSettings()
    // }
    //
    // override fun onDestroy() {
    //     super.onDestroy()
    //
    //     // 停止录音
    //     if (isRecording.get()) {
    //         stopRecording()
    //     }
    //
    //     // 停止说话人录音线程
    //     if (isSpeakerRecording) {
    //         isSpeakerRecording = false
    //         speakerRecordingThread?.let { thread ->
    //             try {
    //                 thread.join(1000) // 最多等待1秒
    //             } catch (e: InterruptedException) {
    //                 Log.w(TAG, "等待说话人录音线程结束被中断", e)
    //             }
    //         }
    //         speakerRecordingThread = null
    //     }
    //
    //     // 释放ASR引擎
    //     if (isInitialized) {
    //         try {
    //             asrEngine.release()
    //         } catch (e: Exception) {
    //             Log.e(TAG, "释放ASR引擎失败", e)
    //         }
    //     }
    //
    //     // 停止时间更新
    //     stopTimeUpdate()
    //
    //     Log.i(TAG, "Activity已销毁")
    // }

    // override fun onRequestPermissionsResult(
    //     requestCode: Int,
    //     permissions: Array<String>,
    //     grantResults: IntArray
    // ) {
    //     super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    //
    //     if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
    //         if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
    //             Log.i(TAG, "录音权限已授予")
    //             showToast("录音权限已授予")
    //         } else {
    //             Log.w(TAG, "录音权限被拒绝")
    //             showToast("录音权限被拒绝，部分功能可能无法使用")
    //         }
    //     }
    // }

    // ==================== 已移除的功能 ====================
    // 声纹管理功能已移至SettingsActivity





    // ==================== AI 功能 ====================
    // ASR优化功能已移至SettingsActivity，这里保留会议总结功能

    /**
     * 自动优化ASR内容 - 停止录音后自动执行
     */
    private fun autoOptimizeAsrContent() {
        // 检查当前LLM是否可用，如果没有配置则静默跳过
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            Log.d(TAG, "当前LLM未配置，跳过自动优化")
            return
        }

        val originalContent = recognitionResults.toString()
        if (originalContent.trim().isEmpty()) {
            Log.d(TAG, "识别结果为空，跳过自动优化")
            return
        }

        // 显示优化提示
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)
        showToast("🔄 正在使用${currentProvider.displayName}自动优化ASR结果...")
        Log.i(TAG, "开始使用${currentProvider.displayName}自动优化ASR结果")

        // 后台执行优化
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.optimizeAsrContent(this@SingleModelActivity, originalContent)

                runOnUiThread {
                    if (result.success) {
                        // 直接替换结果
                        recognitionResults.clear()
                        recognitionResults.append(result.content)
                        baseResultsText = recognitionResults.toString()
                        tvResults.text = baseResultsText

                        // 更新统计
                        wordCount = result.content.length
                        updateStatistics()

                        showToast("✅ ASR结果已自动优化完成")
                        Log.i(TAG, "自动优化ASR结果成功")
                    } else {
                        Log.w(TAG, "自动优化失败: ${result.error}")
                        showToast("⚠️ 自动优化失败，保持原始结果")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    Log.w(TAG, "自动优化异常，保持原始结果", e)
                    showToast("⚠️ 自动优化异常，保持原始结果")
                }
            }
        }
    }

    // ASR优化相关方法已移至LLMManager

    // ASR优化对话框相关方法已移至LLMManager

    /**
     * 生成会议总结
     */
    private fun generateMeetingSummary() {
        if (recognitionResults.isEmpty()) {
            showToast("没有语音识别内容可以总结")
            return
        }

        // 检查当前LLM是否可用
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            showLLMConfigDialog()
            return
        }

        // 获取当前的语音转文字内容
        val speechContent = recognitionResults.toString()

        if (speechContent.trim().isEmpty()) {
            showToast("语音识别内容为空")
            return
        }

        // 获取当前LLM提供商信息
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        // 显示加载对话框
        val loadingDialog = AlertDialog.Builder(this)
            .setTitle("AI会议总结")
            .setMessage("正在使用 ${currentProvider.displayName} 生成会议总结，请稍候...")
            .setCancelable(false)
            .create()
        loadingDialog.show()

        // 在后台线程调用LLM API
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.generateMeetingSummary(this@SingleModelActivity, speechContent)

                runOnUiThread {
                    loadingDialog.dismiss()
                    if (result.success) {
                        showMeetingSummaryDialog(result.content, speechContent)
                    } else {
                        Log.e(TAG, "生成会议总结失败: ${result.error}")
                        showToast("生成会议总结失败: ${result.error}")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    loadingDialog.dismiss()
                    Log.e(TAG, "生成会议总结异常", e)
                    showToast("生成会议总结异常: ${e.message}")
                }
            }
        }
    }

    // Gemini API调用已移至LLMManager

    /**
     * 显示会议总结对话框
     */
    private fun showMeetingSummaryDialog(summary: String, originalContent: String) {
        val dialog = AlertDialog.Builder(this)
            .setTitle("🤖 AI会议总结")
            .create()

        // 创建自定义布局
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        // 总结内容显示区域
        val summaryText = android.widget.TextView(this).apply {
            text = summary
            textSize = 14f
            setPadding(20, 20, 20, 20)
            setBackgroundResource(android.R.drawable.edit_text)
            isVerticalScrollBarEnabled = true
            maxLines = 15
        }

        // 按钮区域
        val buttonLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 20, 0, 0)
        }

        val copySummaryBtn = android.widget.Button(this).apply {
            text = "复制总结"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(0, 0, 10, 0)
            }
        }

        val copyOriginalBtn = android.widget.Button(this).apply {
            text = "复制原文"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(10, 0, 0, 0)
            }
        }

        buttonLayout.addView(copySummaryBtn)
        buttonLayout.addView(copyOriginalBtn)

        layout.addView(summaryText)
        layout.addView(buttonLayout)

        dialog.setView(layout)

        // 设置按钮点击事件
        copySummaryBtn.setOnClickListener {
            copyToClipboard("会议总结", summary)
            showToast("会议总结已复制到剪贴板")
        }

        copyOriginalBtn.setOnClickListener {
            copyToClipboard("会议原文", originalContent)
            showToast("会议原文已复制到剪贴板")
        }

        dialog.setButton(AlertDialog.BUTTON_NEGATIVE, "关闭") { _, _ ->
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * 复制文本到剪贴板
     */
    private fun copyToClipboard(label: String, text: String) {
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText(label, text)
        clipboard.setPrimaryClip(clip)
    }

    /**
     * 显示LLM配置对话框
     */
    private fun showLLMConfigDialog() {
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        AlertDialog.Builder(this)
            .setTitle("LLM 配置")
            .setMessage("当前选择的 ${currentProvider.displayName} 未配置API密钥。\n\n请前往设置页面配置LLM提供商和API密钥。")
            .setPositiveButton("打开设置") { _, _ ->
                openSettings()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    // API密钥配置对话框已移至SettingsActivity

    override fun onResume() {
        super.onResume()
        // 重新加载设置，以便从设置页面返回时更新配置
        loadSettings()
    }

    override fun onDestroy() {
        super.onDestroy()

        // 停止录音
        if (isRecording.get()) {
            stopRecording()
        }

        // 释放ASR引擎
        if (isInitialized) {
            try {
                asrEngine.release()
            } catch (e: Exception) {
                Log.e(TAG, "释放ASR引擎失败", e)
            }
        }

        // 停止时间更新
        stopTimeUpdate()

        Log.i(TAG, "Activity已销毁")
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.i(TAG, "录音权限已授予")
                showToast("录音权限已授予")
            } else {
                Log.w(TAG, "录音权限被拒绝")
                showToast("录音权限被拒绝，部分功能可能无法使用")
            }
        }
    }
}
