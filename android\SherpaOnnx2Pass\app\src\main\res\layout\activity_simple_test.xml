<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@color/background_light"
    android:padding="16dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/primary_blue"
        android:padding="16dp"
        android:layout_marginBottom="24dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🎤 高效语音识别"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="单模型"
            android:textSize="14sp"
            android:textColor="@color/white_alpha_70" />

    </LinearLayout>

    <!-- 状态显示 -->
    <TextView
        android:id="@+id/tv_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="正在加载..."
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/primary_blue"
        android:background="@color/white"
        android:padding="16dp"
        android:layout_marginBottom="8dp"
        android:gravity="center" />

    <!-- 实时预览 -->
    <TextView
        android:id="@+id/tv_preview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text=""
        android:textSize="14sp"
        android:textColor="@color/text_preview"
        android:textStyle="italic"
        android:background="@color/preview_background"
        android:padding="8dp"
        android:layout_marginBottom="16dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:minHeight="40dp" />

    <!-- 识别结果显示 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/white"
        android:padding="16dp"
        android:layout_marginBottom="8dp">

        <TextView
            android:id="@+id/tv_results"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="点击开始录音按钮开始语音识别..."
            android:textSize="14sp"
            android:textColor="@color/text_primary"
            android:lineSpacingExtra="4dp"
            android:textIsSelectable="true" />

    </ScrollView>

    <!-- 统计信息 -->
    <TextView
        android:id="@+id/tv_stats"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="字数: 0 | 识别次数: 0 | 时长: 00:00"
        android:textSize="12sp"
        android:textColor="@color/text_secondary"
        android:background="@color/white"
        android:padding="12dp"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <!-- 主要控制按钮 -->
    <Button
        android:id="@+id/btn_record"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="开始录音"
        android:textSize="16sp"
        android:textStyle="bold"
        android:backgroundTint="@color/primary_blue"
        android:textColor="@color/white"
        android:layout_marginBottom="12dp" />

    <!-- 说话人功能按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <Button
            android:id="@+id/btn_speaker_register"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="🎙️ 注册声纹"
            android:textSize="14sp"
            android:textStyle="bold"
            android:backgroundTint="@color/primary_blue"
            android:textColor="@color/white"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btn_speaker_manage"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="👥 管理声纹"
            android:textSize="14sp"
            android:textStyle="bold"
            android:backgroundTint="@color/accent_green"
            android:textColor="@color/white"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <!-- 功能按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 第一行按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btn_clear"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="清空结果"
                android:textSize="14sp"
                android:backgroundTint="@color/text_secondary"
                android:textColor="@color/white"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/btn_save"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="复制结果"
                android:textSize="14sp"
                android:backgroundTint="@color/accent_green"
                android:textColor="@color/white"
                android:layout_marginHorizontal="4dp" />

            <Button
                android:id="@+id/btn_back"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="退出应用"
                android:textSize="14sp"
                android:backgroundTint="@color/accent_orange"
                android:textColor="@color/white"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- 第二行按钮 - AI功能 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btn_asr_optimize"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="✨ ASR优化"
                android:textSize="14sp"
                android:textStyle="bold"
                android:backgroundTint="@color/accent_green"
                android:textColor="@color/white"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/btn_meeting_summary"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="🤖 AI总结"
                android:textSize="14sp"
                android:textStyle="bold"
                android:backgroundTint="@color/primary_blue"
                android:textColor="@color/white"
                android:layout_marginHorizontal="4dp" />

            <Button
                android:id="@+id/btn_settings"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="⚙️ 设置"
                android:textSize="14sp"
                android:textStyle="bold"
                android:backgroundTint="@color/text_secondary"
                android:textColor="@color/white"
                android:layout_marginStart="4dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 底部说明 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🎉 单模型语音识别 | 说话人识别 | 本地推理 | 实时预览"
        android:textSize="12sp"
        android:textColor="@color/text_hint"
        android:gravity="center"
        android:layout_marginTop="16dp" />

</LinearLayout>
