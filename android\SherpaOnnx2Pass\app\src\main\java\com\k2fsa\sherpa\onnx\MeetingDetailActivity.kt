package com.k2fsa.sherpa.onnx

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView

/**
 * 会议详情页面
 * 采用苹果设计风格，分模块显示会议内容
 */
class MeetingDetailActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MeetingDetailActivity"
    }

    private lateinit var btnBack: ImageButton
    private lateinit var btnDelete: ImageButton
    private lateinit var tvTitle: TextView
    private lateinit var tvDateTime: TextView
    private lateinit var tvDuration: TextView
    private lateinit var tvWordCount: TextView
    private lateinit var tvSpeakerCount: TextView

    // 内容卡片
    private lateinit var cardSummary: CardView
    private lateinit var cardOptimized: CardView
    private lateinit var cardOriginal: CardView

    // 内容文本
    private lateinit var tvSummaryContent: TextView
    private lateinit var tvOptimizedContent: TextView
    private lateinit var tvOriginalContent: TextView

    // 复制按钮
    private lateinit var btnCopySummary: Button
    private lateinit var btnCopyOptimized: Button
    private lateinit var btnCopyOriginal: Button
    private lateinit var btnCopyAll: Button

    private lateinit var meetingRecordManager: MeetingRecordManager
    private var meetingRecord: MeetingRecord? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_meeting_detail)

        initViews()
        initMeetingRecordManager()
        loadMeetingRecord()
    }

    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnDelete = findViewById(R.id.btn_delete)
        tvTitle = findViewById(R.id.tv_meeting_title)
        tvDateTime = findViewById(R.id.tv_meeting_datetime)
        tvDuration = findViewById(R.id.tv_meeting_duration)
        tvWordCount = findViewById(R.id.tv_meeting_word_count)
        tvSpeakerCount = findViewById(R.id.tv_meeting_speaker_count)

        cardSummary = findViewById(R.id.card_summary)
        cardOptimized = findViewById(R.id.card_optimized)
        cardOriginal = findViewById(R.id.card_original)

        tvSummaryContent = findViewById(R.id.tv_summary_content)
        tvOptimizedContent = findViewById(R.id.tv_optimized_content)
        tvOriginalContent = findViewById(R.id.tv_original_content)

        btnCopySummary = findViewById(R.id.btn_copy_summary)
        btnCopyOptimized = findViewById(R.id.btn_copy_optimized)
        btnCopyOriginal = findViewById(R.id.btn_copy_original)
        btnCopyAll = findViewById(R.id.btn_copy_all)

        // 设置点击事件
        btnBack.setOnClickListener { finish() }
        btnDelete.setOnClickListener { showDeleteDialog() }
        btnCopySummary.setOnClickListener { copyContent("智能总结", tvSummaryContent.text.toString()) }
        btnCopyOptimized.setOnClickListener { copyContent("优化内容", tvOptimizedContent.text.toString()) }
        btnCopyOriginal.setOnClickListener { copyContent("原始记录", tvOriginalContent.text.toString()) }
        btnCopyAll.setOnClickListener { copyAllContent() }
    }

    private fun initMeetingRecordManager() {
        meetingRecordManager = MeetingRecordManager.getInstance(this)
    }

    private fun loadMeetingRecord() {
        val recordId = intent.getStringExtra("meeting_record_id")
        if (recordId.isNullOrEmpty()) {
            Log.e(TAG, "会议记录ID为空")
            showToast("会议记录ID无效")
            finish()
            return
        }

        meetingRecord = meetingRecordManager.getRecordById(recordId)
        if (meetingRecord == null) {
            Log.e(TAG, "找不到会议记录: $recordId")
            showToast("找不到会议记录")
            finish()
            return
        }

        displayMeetingRecord(meetingRecord!!)
    }

    private fun displayMeetingRecord(record: MeetingRecord) {
        // 设置基本信息
        tvTitle.text = record.title
        tvDateTime.text = record.getFormattedDateTime()
        tvDuration.text = "时长: ${record.getFormattedDuration()}"
        tvWordCount.text = "字数: ${record.wordCount}"
        
        if (record.speakerCount > 0) {
            tvSpeakerCount.text = "说话人: ${record.speakerCount} 人"
            tvSpeakerCount.visibility = View.VISIBLE
        } else {
            tvSpeakerCount.visibility = View.GONE
        }

        // 显示智能总结
        if (record.hasSummaryContent()) {
            cardSummary.visibility = View.VISIBLE
            tvSummaryContent.text = record.summaryContent
        } else {
            cardSummary.visibility = View.GONE
        }

        // 显示优化内容
        if (record.hasOptimizedContent()) {
            cardOptimized.visibility = View.VISIBLE
            tvOptimizedContent.text = record.optimizedContent
        } else {
            cardOptimized.visibility = View.GONE
        }

        // 显示原始内容
        tvOriginalContent.text = record.originalContent
    }

    private fun copyContent(label: String, content: String) {
        if (content.isBlank()) {
            showToast("内容为空")
            return
        }

        try {
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText(label, content)
            clipboard.setPrimaryClip(clip)
            showToast("$label 已复制到剪贴板")
        } catch (e: Exception) {
            Log.e(TAG, "复制内容失败", e)
            showToast("复制失败: ${e.message}")
        }
    }

    private fun copyAllContent() {
        val record = meetingRecord ?: return
        
        try {
            val fullContent = record.getFullContent()
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("完整会议记录", fullContent)
            clipboard.setPrimaryClip(clip)
            showToast("完整会议记录已复制到剪贴板")
        } catch (e: Exception) {
            Log.e(TAG, "复制完整内容失败", e)
            showToast("复制失败: ${e.message}")
        }
    }

    private fun showDeleteDialog() {
        val record = meetingRecord ?: return

        AlertDialog.Builder(this)
            .setTitle("删除会议记录")
            .setMessage("确定要删除「${record.title}」吗？\n\n此操作不可撤销。")
            .setPositiveButton("删除") { _, _ ->
                deleteRecord()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun deleteRecord() {
        val record = meetingRecord ?: return

        try {
            val success = meetingRecordManager.deleteRecord(record.id)
            if (success) {
                showToast("会议记录已删除")
                finish()
            } else {
                showToast("删除失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除会议记录失败", e)
            showToast("删除失败: ${e.message}")
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
