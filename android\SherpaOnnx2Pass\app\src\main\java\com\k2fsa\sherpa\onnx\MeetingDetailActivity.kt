package com.k2fsa.sherpa.onnx

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.media.MediaPlayer
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import kotlinx.coroutines.*
import java.io.IOException

/**
 * 会议详情页面
 * 采用苹果设计风格，分模块显示会议内容
 */
class MeetingDetailActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MeetingDetailActivity"
    }

    private lateinit var btnBack: ImageButton
    private lateinit var btnDelete: ImageButton
    private lateinit var tvTitle: TextView
    private lateinit var tvDateTime: TextView
    private lateinit var tvDuration: TextView
    private lateinit var tvWordCount: TextView
    private lateinit var tvSpeakerCount: TextView

    // 内容卡片
    private lateinit var cardAudio: CardView
    private lateinit var cardSummary: CardView
    private lateinit var cardOptimized: CardView
    private lateinit var cardOriginal: CardView

    // 内容文本
    private lateinit var tvSummaryContent: TextView
    private lateinit var tvOptimizedContent: TextView
    private lateinit var tvOriginalContent: TextView

    // 录音播放相关
    private lateinit var btnPlayAudio: Button
    private lateinit var tvAudioInfo: TextView
    private var mediaPlayer: MediaPlayer? = null
    private var isPlaying = false

    // 复制按钮
    private lateinit var btnCopySummary: Button
    private lateinit var btnCopyOptimized: Button
    private lateinit var btnCopyOriginal: Button
    private lateinit var btnCopyAll: Button

    // AI处理按钮
    private lateinit var btnGenerateSummary: Button
    private lateinit var btnGenerateOptimized: Button

    private lateinit var meetingRecordManager: MeetingRecordManager
    private var meetingRecord: MeetingRecord? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_meeting_detail)

        initViews()
        initMeetingRecordManager()
        loadMeetingRecord()
    }

    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnDelete = findViewById(R.id.btn_delete)
        tvTitle = findViewById(R.id.tv_meeting_title)
        tvDateTime = findViewById(R.id.tv_meeting_datetime)
        tvDuration = findViewById(R.id.tv_meeting_duration)
        tvWordCount = findViewById(R.id.tv_meeting_word_count)
        tvSpeakerCount = findViewById(R.id.tv_meeting_speaker_count)

        cardAudio = findViewById(R.id.card_audio)
        cardSummary = findViewById(R.id.card_summary)
        cardOptimized = findViewById(R.id.card_optimized)
        cardOriginal = findViewById(R.id.card_original)

        tvSummaryContent = findViewById(R.id.tv_summary_content)
        tvOptimizedContent = findViewById(R.id.tv_optimized_content)
        tvOriginalContent = findViewById(R.id.tv_original_content)

        btnPlayAudio = findViewById(R.id.btn_play_audio)
        tvAudioInfo = findViewById(R.id.tv_audio_info)

        btnCopySummary = findViewById(R.id.btn_copy_summary)
        btnCopyOptimized = findViewById(R.id.btn_copy_optimized)
        btnCopyOriginal = findViewById(R.id.btn_copy_original)
        btnCopyAll = findViewById(R.id.btn_copy_all)

        btnGenerateSummary = findViewById(R.id.btn_generate_summary)
        btnGenerateOptimized = findViewById(R.id.btn_generate_optimized)

        // 设置点击事件
        btnBack.setOnClickListener { finish() }
        btnDelete.setOnClickListener { showDeleteDialog() }
        btnPlayAudio.setOnClickListener { toggleAudioPlayback() }
        btnCopySummary.setOnClickListener { copyContent("智能总结", tvSummaryContent.text.toString()) }
        btnCopyOptimized.setOnClickListener { copyContent("优化内容", tvOptimizedContent.text.toString()) }
        btnCopyOriginal.setOnClickListener { copyContent("原始记录", tvOriginalContent.text.toString()) }
        btnCopyAll.setOnClickListener { copyAllContent() }

        // AI处理按钮点击事件
        btnGenerateSummary.setOnClickListener { generateMeetingSummary() }
        btnGenerateOptimized.setOnClickListener { generateOptimizedContent() }
    }

    private fun initMeetingRecordManager() {
        meetingRecordManager = MeetingRecordManager.getInstance(this)
    }

    private fun loadMeetingRecord() {
        val recordId = intent.getStringExtra("meeting_record_id")
        if (recordId.isNullOrEmpty()) {
            Log.e(TAG, "会议记录ID为空")
            showToast("会议记录ID无效")
            finish()
            return
        }

        meetingRecord = meetingRecordManager.getRecordById(recordId)
        if (meetingRecord == null) {
            Log.e(TAG, "找不到会议记录: $recordId")
            showToast("找不到会议记录")
            finish()
            return
        }

        displayMeetingRecord(meetingRecord!!)
    }

    private fun displayMeetingRecord(record: MeetingRecord) {
        // 设置基本信息
        tvTitle.text = record.title
        tvDateTime.text = record.getFormattedDateTime()
        tvDuration.text = "时长: ${record.getFormattedDuration()}"
        tvWordCount.text = "字数: ${record.wordCount}"
        
        if (record.speakerCount > 0) {
            tvSpeakerCount.text = "说话人: ${record.speakerCount} 人"
            tvSpeakerCount.visibility = View.VISIBLE
        } else {
            tvSpeakerCount.visibility = View.GONE
        }

        // 显示录音文件
        if (record.hasAudioFile()) {
            cardAudio.visibility = View.VISIBLE
            val audioRecordingManager = AudioRecordingManager(this)
            val fileSize = audioRecordingManager.getRecordingSize(record.audioFilePath!!)
            tvAudioInfo.text = "录音文件 (${String.format("%.1f", fileSize)} MB)"
        } else {
            cardAudio.visibility = View.GONE
        }

        // 始终显示智能总结卡片
        cardSummary.visibility = View.VISIBLE
        if (record.hasSummaryContent()) {
            tvSummaryContent.text = record.summaryContent
            btnCopySummary.visibility = View.VISIBLE
            btnGenerateSummary.visibility = View.GONE
        } else {
            tvSummaryContent.text = "点击下方按钮生成AI智能总结"
            tvSummaryContent.setTextColor(getColor(R.color.apple_secondary_label))
            btnCopySummary.visibility = View.GONE
            btnGenerateSummary.visibility = View.VISIBLE
        }

        // 始终显示优化内容卡片
        cardOptimized.visibility = View.VISIBLE
        if (record.hasOptimizedContent()) {
            tvOptimizedContent.text = record.optimizedContent
            btnCopyOptimized.visibility = View.VISIBLE
            btnGenerateOptimized.visibility = View.GONE
        } else {
            tvOptimizedContent.text = "点击下方按钮生成AI优化内容"
            tvOptimizedContent.setTextColor(getColor(R.color.apple_secondary_label))
            btnCopyOptimized.visibility = View.GONE
            btnGenerateOptimized.visibility = View.VISIBLE
        }

        // 显示原始内容
        tvOriginalContent.text = record.originalContent
    }

    private fun copyContent(label: String, content: String) {
        if (content.isBlank()) {
            showToast("内容为空")
            return
        }

        try {
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText(label, content)
            clipboard.setPrimaryClip(clip)
            showToast("$label 已复制到剪贴板")
        } catch (e: Exception) {
            Log.e(TAG, "复制内容失败", e)
            showToast("复制失败: ${e.message}")
        }
    }

    private fun copyAllContent() {
        val record = meetingRecord ?: return
        
        try {
            val fullContent = record.getFullContent()
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("完整会议记录", fullContent)
            clipboard.setPrimaryClip(clip)
            showToast("完整会议记录已复制到剪贴板")
        } catch (e: Exception) {
            Log.e(TAG, "复制完整内容失败", e)
            showToast("复制失败: ${e.message}")
        }
    }

    private fun showDeleteDialog() {
        val record = meetingRecord ?: return

        AlertDialog.Builder(this)
            .setTitle("删除会议记录")
            .setMessage("确定要删除「${record.title}」吗？\n\n此操作不可撤销。")
            .setPositiveButton("删除") { _, _ ->
                deleteRecord()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun deleteRecord() {
        val record = meetingRecord ?: return

        try {
            val success = meetingRecordManager.deleteRecord(record.id)
            if (success) {
                showToast("会议记录已删除")
                finish()
            } else {
                showToast("删除失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除会议记录失败", e)
            showToast("删除失败: ${e.message}")
        }
    }

    /**
     * 生成会议总结
     */
    private fun generateMeetingSummary() {
        val record = meetingRecord ?: return

        if (record.originalContent.trim().isEmpty()) {
            showToast("原始内容为空，无法生成总结")
            return
        }

        // 检查当前LLM是否可用
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            showLLMConfigDialog()
            return
        }

        // 获取当前LLM提供商信息
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        // 显示加载对话框
        val loadingDialog = AlertDialog.Builder(this)
            .setTitle("AI会议总结")
            .setMessage("正在使用 ${currentProvider.displayName} 生成会议总结，请稍候...")
            .setCancelable(false)
            .create()
        loadingDialog.show()

        // 在后台线程调用LLM API
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.generateMeetingSummary(this@MeetingDetailActivity, record.originalContent)

                runOnUiThread {
                    loadingDialog.dismiss()
                    if (result.success) {
                        // 更新UI
                        tvSummaryContent.text = result.content
                        tvSummaryContent.setTextColor(getColor(R.color.apple_label))
                        btnCopySummary.visibility = View.VISIBLE
                        btnGenerateSummary.visibility = View.GONE

                        // 更新数据库
                        updateMeetingRecordSummary(result.content)

                        showToast("✅ 会议总结生成成功")
                    } else {
                        Log.e(TAG, "生成会议总结失败: ${result.error}")
                        showToast("生成会议总结失败: ${result.error}")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    loadingDialog.dismiss()
                    Log.e(TAG, "生成会议总结异常", e)
                    showToast("生成会议总结异常: ${e.message}")
                }
            }
        }
    }

    /**
     * 生成优化内容
     */
    private fun generateOptimizedContent() {
        val record = meetingRecord ?: return

        if (record.originalContent.trim().isEmpty()) {
            showToast("原始内容为空，无法生成优化内容")
            return
        }

        // 检查当前LLM是否可用
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            showLLMConfigDialog()
            return
        }

        // 获取当前LLM提供商信息
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        // 显示加载对话框
        val loadingDialog = AlertDialog.Builder(this)
            .setTitle("AI内容优化")
            .setMessage("正在使用 ${currentProvider.displayName} 优化内容，请稍候...")
            .setCancelable(false)
            .create()
        loadingDialog.show()

        // 在后台线程调用LLM API
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.optimizeAsrContent(this@MeetingDetailActivity, record.originalContent)

                runOnUiThread {
                    loadingDialog.dismiss()
                    if (result.success) {
                        // 更新UI
                        tvOptimizedContent.text = result.content
                        tvOptimizedContent.setTextColor(getColor(R.color.apple_label))
                        btnCopyOptimized.visibility = View.VISIBLE
                        btnGenerateOptimized.visibility = View.GONE

                        // 更新数据库
                        updateMeetingRecordOptimized(result.content)

                        showToast("✅ 内容优化完成")
                    } else {
                        Log.e(TAG, "生成优化内容失败: ${result.error}")
                        showToast("生成优化内容失败: ${result.error}")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    loadingDialog.dismiss()
                    Log.e(TAG, "生成优化内容异常", e)
                    showToast("生成优化内容异常: ${e.message}")
                }
            }
        }
    }

    /**
     * 更新会议记录的总结内容
     */
    private fun updateMeetingRecordSummary(summaryContent: String) {
        val record = meetingRecord ?: return

        try {
            val updatedRecord = record.copy(summaryContent = summaryContent)
            val success = meetingRecordManager.saveMeetingRecord(updatedRecord)
            if (success) {
                meetingRecord = updatedRecord
                Log.d(TAG, "会议记录总结内容已更新")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新会议记录总结内容失败", e)
        }
    }

    /**
     * 更新会议记录的优化内容
     */
    private fun updateMeetingRecordOptimized(optimizedContent: String) {
        val record = meetingRecord ?: return

        try {
            val updatedRecord = record.copy(optimizedContent = optimizedContent)
            val success = meetingRecordManager.saveMeetingRecord(updatedRecord)
            if (success) {
                meetingRecord = updatedRecord
                Log.d(TAG, "会议记录优化内容已更新")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新会议记录优化内容失败", e)
        }
    }

    /**
     * 显示LLM配置对话框
     */
    private fun showLLMConfigDialog() {
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        AlertDialog.Builder(this)
            .setTitle("LLM 配置")
            .setMessage("当前选择的 ${currentProvider.displayName} 未配置API密钥。\n\n请前往设置页面配置LLM提供商和API密钥。")
            .setPositiveButton("打开设置") { _, _ ->
                val intent = android.content.Intent(this, SettingsActivity::class.java)
                startActivity(intent)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 切换录音播放状态
     */
    private fun toggleAudioPlayback() {
        val record = meetingRecord ?: return

        if (!record.hasAudioFile()) {
            showToast("录音文件不存在")
            return
        }

        try {
            if (isPlaying) {
                stopAudioPlayback()
            } else {
                startAudioPlayback(record.audioFilePath!!)
            }
        } catch (e: Exception) {
            Log.e(TAG, "录音播放操作失败", e)
            showToast("录音播放失败: ${e.message}")
        }
    }

    /**
     * 开始播放录音
     */
    private fun startAudioPlayback(audioFilePath: String) {
        try {
            mediaPlayer = MediaPlayer().apply {
                setDataSource(audioFilePath)
                prepareAsync()
                setOnPreparedListener {
                    start()
                    <EMAIL> = true
                    btnPlayAudio.text = "⏸️ 暂停播放"
                    Log.i(TAG, "开始播放录音: $audioFilePath")
                }
                setOnCompletionListener {
                    stopAudioPlayback()
                    showToast("录音播放完成")
                }
                setOnErrorListener { _, what, extra ->
                    Log.e(TAG, "录音播放错误: what=$what, extra=$extra")
                    stopAudioPlayback()
                    showToast("录音播放出错")
                    true
                }
            }
        } catch (e: IOException) {
            Log.e(TAG, "录音播放初始化失败", e)
            showToast("录音文件无法播放")
        }
    }

    /**
     * 停止播放录音
     */
    private fun stopAudioPlayback() {
        try {
            mediaPlayer?.let { player ->
                if (player.isPlaying) {
                    player.stop()
                }
                player.release()
            }
            mediaPlayer = null
            isPlaying = false
            btnPlayAudio.text = "▶️ 播放录音"
            Log.i(TAG, "停止播放录音")
        } catch (e: Exception) {
            Log.e(TAG, "停止录音播放失败", e)
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 释放MediaPlayer资源
        stopAudioPlayback()
    }
}
